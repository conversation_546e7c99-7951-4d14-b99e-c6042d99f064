package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/form"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/google/uuid"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/maps"
	"server/server"
	"server/server/database"
	"server/server/factions/items"
	"server/server/language"
	"server/server/ui"
	"server/server/user"
	"server/server/utils"
	"slices"
	"strings"
	"time"
)

type FactionManageCommand struct{}

func (FactionManageCommand) Run(src cmd.Source, _ *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		FactionManageUI{}.SendTo(pl)
	}
}

type FactionManageUI struct{}

func (f FactionManageUI) Submit(submitter form.Submitter, button form.Button, tx *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	switch button.Text {
	case text.Colourf("<gold>Create a faction</gold>"):
		FactionCreateUI{}.SendTo(pl)
	case text.Colourf("<gold>Allies</gold>"):
		FactionAlliesUI{}.SendTo(pl)
	case text.Colourf("<gold>Members</gold>"):
		FactionMembersUI{}.SendTo(pl)
	case text.Colourf("<gold>Apply for a faction (to join)</gold>"):
		FactionApplyUI{}.SendTo(pl)
	case text.Colourf("<gold>Request allianace</gold>"):
		FactionAllianceUI{}.SendTo(pl)
	case text.Colourf("<gold>View requests</gold>"):
		FactionRequestsUI{}.SendTo(pl)
	case text.Colourf("<gold>View claim area</gold>"):
		FactionClaimUI{}.SendTo(pl)
	case text.Colourf("<dark-red>Disband your faction</dark-red>"):
		u.Data.Faction.Role = database.Member

		n := u.Data.Faction.Name
		fac := utils.Panics(database.DB.FindFaction(n))

		for ally := range fac.Allies {
			allyFac := utils.Panics(database.DB.FindFaction(ally))
			var newAllysAllies map[string]time.Time

			//filter
			for a, joinAt := range allyFac.Allies {
				if a != ally {
					newAllysAllies[a] = joinAt
				}
			}

			allyFac.Allies = newAllysAllies
			utils.Panic(database.DB.SaveFaction(allyFac))
		}

		for _, id := range fac.Members {
			d := user.GetUserByUUID(id).Data
			d.Faction.Name = ""

			if e, ok := server.MCServer.PlayerByName(d.Username); ok {
				if ent, ok := e.Entity(tx); ok {
					ent.(*player.Player).SetNameTag(user.FactionNameDisplay.Name(d))
				} else {
					e.ExecWorld(func(tx *world.Tx, e world.Entity) {
						ent.(*player.Player).SetNameTag(user.FactionNameDisplay.Name(d))
					})
				}
			}
		}

		utils.Panic(database.DB.DeleteFaction(n))

		pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Disband, server.Config.Prefix))
	case text.Colourf("<red>Leave the faction</red>"):
		u.Data.Faction.Role = database.Member

		var newFacMembers []uuid.UUID
		for _, id := range u.Data.Faction.Faction().Members {
			if id != pl.UUID() {
				newFacMembers = append(newFacMembers, id)
			}
		}
		fac := u.Data.Faction.Faction()
		fac.Members = newFacMembers
		utils.Panic(database.DB.SaveFaction(fac))

		u.Data.Faction.Name = ""
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Leave, server.Config.Prefix))
	}
}

func (f FactionManageUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionManageUI{}, text.Colourf("<green>Factions Management</green>"))
	if u.Data.Faction.HasFaction() {
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>Allies</gold>"), "", ""))
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>Members</gold>"), "", ""))

		if u.Data.Faction.Role == database.Leader || u.Data.Faction.Role == database.CoLeader {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>Request allianace</gold>"), "", ""))
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>View requests</gold>"), "", ""))
		}

		if u.Data.Faction.Role == database.Leader {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>View claim area</gold>"), "", ""))
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<dark-red>Disband your faction</dark-red>"), "", ""))
		} else {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<red>Leave the faction</red>"), "", ""))
		}
	} else {
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>Create a faction</gold>"), "", ""))
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<gold>Apply for a faction (to join)</gold>"), "", ""))
	}
	pl.SendForm(fm)
}

type FactionCreateUI struct {
	FactionName form.Input
}

func (f FactionCreateUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	n := f.FactionName.Value()
	if len(n) < 2 || len(n) > 14 {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Create.Error.BadLength))
		return
	}

	if _, err := database.DB.FindFaction(n); err == nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Create.Error.ExistingName))
		return
	}

	utils.Panic(database.DB.CreateFaction(&database.FactionData{
		Name:    n,
		Allies:  map[string]time.Time{},
		Members: []uuid.UUID{pl.UUID()},
	}))

	u := user.GetUser(pl)
	u.Data.Faction.Name = n
	u.Data.Faction.Role = database.Leader
	u.Data.Faction.FirstJoined = time.Now()

	pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
	pl.Message(text.Colourf(language.Translate(pl).FactionManage.Create.Success, server.Config.Prefix, n))
}

func (f FactionCreateUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionCreateUI) SendTo(pl *player.Player) {
	fm := form.New(
		FactionCreateUI{
			FactionName: form.NewInput("Faction Name", "", "Enter a faction name"),
		}, text.Colourf("<green>Faction Creation</green>"))
	pl.SendForm(fm)
}

type FactionAlliesUI struct{}

func (f FactionAlliesUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	FactionAllyUI{}.SendTo(pl, utils.Panics(database.DB.FindFaction(ui.Load[string](pl, button.Text))))
}

func (f FactionAlliesUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAlliesUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionAlliesUI{}, text.Colourf("<green>Faction Allies</green>"))
	allies := u.Data.Faction.Faction().Allies
	if len(allies) == 0 {
		fm = fm.WithBody(text.Colourf("<white>You have no allies.</white>"))
	} else {
		for name := range allies {
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>%v</dark-grey>", name), "", name))
		}
	}
	pl.SendForm(fm)
}

type FactionAllyUI struct {
	Back form.Button
}

func (f FactionAllyUI) Submit(submitter form.Submitter, _ form.Button, _ *world.Tx) {
	FactionAlliesUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAllyUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionAlliesUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAllyUI) SendTo(pl *player.Player, fac *database.FactionData) {
	fm := form.NewMenu(FactionAllyUI{
		Back: ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Back</dark-grey>"), "", ""),
	}, text.Colourf("<green>Ally Info</green>"))
	fm = fm.WithBody(text.Colourf(
		"<green>Name: <yellow>%v</yellow>\n"+
			"Owner: <yellow>%v</yellow>\n"+
			"Allies: <yellow>%v</yellow>\n"+
			"Total members: <yellow>%v</yellow>\n"+
			"Total strength: <yellow>%v</yellow></green>",
		fac.Name,
		user.FactionNameDisplay.Name(user.GetUserByUUID(fac.Members[0]).Data),
		strings.Join(maps.Keys(fac.Allies)[:], ", "),
		len(fac.Members),
		fac.Strength,
	))
	pl.SendForm(fm)
}

type FactionMembersUI struct{}

func (f FactionMembersUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	FactionMemberUI{}.SendTo(pl, ui.Load[*user.User](pl, button.Text))
}

func (f FactionMembersUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionMembersUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionMembersUI{}, text.Colourf("<green>Faction Members</green>"))
	for _, id := range u.Data.Faction.Faction().Members {
		um := user.GetUserByUUID(id)
		fm = fm.WithButtons(ui.AddButtonWithValue(pl, user.FactionNameDisplay.Name(um.Data), "", um))
	}
	pl.SendForm(fm)
}

type FactionMemberUI struct {
	member *user.User
}

func NewFactionMemberUI(member *user.User) FactionMemberUI {
	return FactionMemberUI{member: member}
}

func (f FactionMemberUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	switch ui.Load[string](pl, button.Text) {
	case "kick":
		fac := utils.Panics(database.DB.FindFaction(f.member.Data.Faction.Name))
		var newMembers []uuid.UUID
		for _, id := range fac.Members {
			if id != f.member.Player().UUID() {
				newMembers = append(newMembers, id)
			}
		}
		fac.Members = newMembers
		utils.Panic(database.DB.SaveFaction(fac))

		f.member.Data.Faction.Name = ""
		pl.Message(language.Translate(pl).FactionManage.Members.Kick, server.Config.Prefix, user.FactionNameDisplay.Name(f.member.Data))
	case "role":
		NewFactionMemberRoleUI(f.member).SendTo(pl)
	case "back":
		FactionMembersUI{}.SendTo(submitter.(*player.Player))
	}
}

func (f FactionMemberUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionMembersUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionMemberUI) SendTo(pl *player.Player, member *user.User) {
	u := user.GetUser(pl)
	fm := form.NewMenu(NewFactionMemberUI(member), text.Colourf("<green>Member Info</green>"))
	if u.Data.Faction.Faction().Members[0] == pl.UUID() && member.Player().UUID() != pl.UUID() || u.Data.Faction.Role < member.Data.Faction.Role {
		fm = fm.WithButtons(
			ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Kick</dark-grey>"), "", "kick"),
			ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Role</dark-grey>"), "", "role"),
		)
	}
	fm = fm.WithButtons(
		ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Back</dark-grey>"), "", "back"),
	)
	fm = fm.WithBody(text.Colourf(
		"<green>Name: <yellow>%v</yellow>\n"+
			"Joined at: <yellow>%v</yellow>\n"+
			"Role: <yellow>%v</yellow>\n"+
			"Doubloons: <yellow>%v</yellow>\n"+
			"Strength: <yellow>%v</yellow></green>",
		user.FactionNameDisplay.Name(member.Data),
		member.Data.Faction.FirstJoined,
		database.RolePrefixes[member.Data.Faction.Role],
		member.Data.Faction.Stats.Doubloons,
		member.Data.Faction.Stats.Strength,
	))
	pl.SendForm(fm)
}

type FactionMemberRoleUI struct {
	member *user.User

	Role form.Dropdown
}

func NewFactionMemberRoleUI(member *user.User) FactionMemberRoleUI {
	return FactionMemberRoleUI{
		member: member,
		Role:   form.NewDropdown("Role", database.RolePrefixes[1:], int(member.Data.Faction.Role)),
	}
}

func (f FactionMemberRoleUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	f.member.Data.Faction.Role = database.Role(f.Role.Value())
	pl.Message(language.Translate(pl).FactionManage.Members.Role, server.Config.Prefix, user.FactionNameDisplay.Name(f.member.Data), database.RolePrefixes[f.Role.Value()])
}

func (f FactionMemberRoleUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionMemberUI{}.SendTo(submitter.(*player.Player), f.member)
}

func (f FactionMemberRoleUI) SendTo(pl *player.Player) {
	fm := form.New(NewFactionMemberRoleUI(f.member), text.Colourf("<green>Change %v's role</green>", user.FactionNameDisplay.Name(f.member.Data)))
	pl.SendForm(fm)
}

type FactionApplyUI struct {
	FactionName      form.Input
	ApplyDescription form.Input
}

func (f FactionApplyUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	n := f.FactionName.Value()
	d := f.ApplyDescription.Value()
	if d == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyDescription))
		return
	}
	if len(d) < 10 {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.ShortDescription))
		return
	}
	if n == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyFactionName))
		return
	}

	fac, err := database.DB.FindFaction(n)
	if err != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.FactionNotExist, n))
		return
	}
	if u.Data.Faction.HasFaction() {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.AlreadyInFaction))
		return
	}
	if u.Data.Faction.Request != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.RequestReplaced, u.Data.Faction.Request.TargetFactionName))
	}

	r := &database.Request{
		SentBy:            pl.UUID(),
		SentAt:            time.Now(),
		SenderFactionName: "",
		TargetFactionName: n,
		Type:              database.JoinFaction,
		Description:       d,
	}

	u.Data.Faction.Request = r
	fac.Requests = append(fac.Requests, r)
	pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Success, server.Config.Prefix))
}

func (f FactionApplyUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionApplyUI) SendTo(pl *player.Player) {
	fm := form.New(FactionApplyUI{
		FactionName:      form.NewInput("What is the name of the faction you are applying for?", "", "Enter a faction name"),
		ApplyDescription: form.NewInput("Please write the reason why you want to apply [at least 10 characters]", "", "Enter a reason of application"),
	}, text.Colourf("<green>Application Request</green>"))
	pl.SendForm(fm)
}

type FactionAllianceUI struct {
	FactionName     form.Input
	AllyDescription form.Input
}

func (f FactionAllianceUI) Submit(submitter form.Submitter, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	n := f.FactionName.Value()
	d := f.AllyDescription.Value()
	if d == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyDescription))
		return
	}
	if len(d) < 10 {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyDescription))
		return
	}
	if n == "" {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.EmptyFactionName))
		return
	}

	fac, err := database.DB.FindFaction(n)
	if err != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.FactionNotExist, n))
		return
	}
	if fac.Name == u.Data.Faction.Name {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.SameFaction))
		return
	}

	if u.Data.Faction.Request != nil {
		pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Error.RequestReplaced))
	}

	r := &database.Request{
		SentBy:            pl.UUID(),
		SentAt:            time.Now(),
		SenderFactionName: u.Data.Faction.Name,
		TargetFactionName: n,
		Type:              database.AllyFaction,
		Description:       d,
	}

	u.Data.Faction.Request = r
	fac.Requests = append(fac.Requests, r)
	pl.Message(text.Colourf(language.Translate(pl).FactionManage.Request.Success, server.Config.Prefix))
}

func (f FactionAllianceUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionAllianceUI) SendTo(pl *player.Player) {
	fm := form.New(FactionAllianceUI{
		FactionName:     form.NewInput("What is the name of the faction you are trying to ally?", "", "Enter a faction name"),
		AllyDescription: form.NewInput("Please write the reason why you want to ally with us [at least 10 characters]", "", "Enter a reason to ally"),
	}, text.Colourf("<green>Alliance Request</green>"))
	pl.SendForm(fm)
}

type FactionRequestsUI struct{}

func (f FactionRequestsUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	FactionRequestUI{}.SendTo(pl, ui.Load[*database.Request](pl, button.Text))
}

func (f FactionRequestsUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionRequestsUI) SendTo(pl *player.Player) {
	u := user.GetUser(pl)
	fm := form.NewMenu(FactionRequestsUI{}, text.Colourf("<green>Faction Requests</green>"))
	reqSlice := u.Data.Faction.Faction().Requests
	if len(reqSlice) == 0 {
		fm = fm.WithBody(text.Colourf("<white>There are currently no requests.</white>"))
	} else {
		slices.Reverse(reqSlice)
		for _, r := range reqSlice {
			str := ""
			if r.SenderFactionName != "" {
				str = text.Colourf(" from <grey>%v</grey>", r.SenderFactionName)
			}
			fm = fm.WithButtons(ui.AddButtonWithValue(pl, text.Colourf("<green>%v%v\n<italic><grey>Click for more information</grey></italic></green>", r.LongType(), str), "", r))
		}
	}
	pl.SendForm(fm)
}

type FactionRequestUI struct {
	request *database.Request
}

func NewFactionRequestUI(r *database.Request) FactionRequestUI {
	return FactionRequestUI{request: r}
}

func (f FactionRequestUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	u := user.GetUser(pl)
	ut := user.GetUserByUUID(f.request.SentBy)
	value := ui.Load[string](pl, button.Text)

	if value != "back" {
		fac := u.Data.Faction.Faction()
		if ut != nil {
			if value == "accept" {
				switch f.request.Type {
				case database.JoinFaction:
					fac.Members = append(fac.Members)
					pd := user.GetUserByUUID(f.request.SentBy).Data
					pd.Faction.Name = fac.Name
					pd.Faction.Role = database.Member
					pd.Faction.FirstJoined = time.Now()
				case database.AllyFaction:
					senderFac := utils.Panics(database.DB.FindFaction(f.request.SenderFactionName))
					fac.Allies[f.request.SenderFactionName] = time.Now()
					senderFac.Allies[fac.Name] = time.Now()
					utils.Panic(database.DB.SaveFaction(senderFac))
				}
				ut.Player().Message(text.Colourf(language.Translate(ut.Player()).FactionManage.Request.Accepted, server.Config.Prefix))
			} else {
				ut.Player().Message(text.Colourf(language.Translate(ut.Player()).FactionManage.Request.Rejected, server.Config.Prefix))
			}
		}
		fac.Requests = utils.Filter(fac.Requests, func(r1 *database.Request) bool {
			return r1 != f.request
		})
		utils.Panic(database.DB.SaveFaction(fac))
		ut.Data.Faction.Request = nil
	} else {
		FactionRequestsUI{}.SendTo(submitter.(*player.Player))
	}
}

func (f FactionRequestUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionRequestsUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionRequestUI) SendTo(pl *player.Player, r *database.Request) {
	fm := form.NewMenu(NewFactionRequestUI(r), text.Colourf("<green>%v</green>", r.LongType()))
	fm = fm.WithButtons(
		ui.AddButtonWithValue(pl, text.Colourf("<green>Accept</green>"), "", "accept"),
		ui.AddButtonWithValue(pl, text.Colourf("<red>Reject</red>"), "", "reject"),
		ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Back</dark-grey>"), "", "back"),
	)
	fm = fm.WithBody(text.Colourf(
		"<green>Sent By: <yellow>%v</yellow>\n"+
			"Sent At: <yellow>%v</yellow>\n"+
			"Message: <yellow>%v</yellow></green>",
		user.FactionNameDisplay.Name(user.GetUserByUUID(r.SentBy).Data),
		r.SentAt.String(),
		r.Description,
	))
	pl.SendForm(fm)
}

type FactionClaimUI struct {
	ObtainShovel form.Button
	Back         form.Button
}

func (f FactionClaimUI) Submit(submitter form.Submitter, button form.Button, _ *world.Tx) {
	pl := submitter.(*player.Player)
	switch button {
	case f.ObtainShovel:
		if _, err := pl.Inventory().AddItem(items.ClaimShovel{}.Stack()); err != nil {
			pl.Drop(items.ClaimShovel{}.Stack())
			pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
		}

		pl.Message(text.Colourf(language.Translate(pl).GiveShovel, server.Config.Prefix))
	case f.Back:
		FactionManageUI{}.SendTo(submitter.(*player.Player))
	}
}

func (f FactionClaimUI) Close(submitter form.Submitter, _ *world.Tx) {
	FactionManageUI{}.SendTo(submitter.(*player.Player))
}

func (f FactionClaimUI) SendTo(pl *player.Player) {
	fm := form.NewMenu(FactionClaimUI{
		ObtainShovel: ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Obtain Claim Shovel</dark-grey>"), "", ""),
		Back:         ui.AddButtonWithValue(pl, text.Colourf("<dark-grey>Back</dark-grey>"), "", ""),
	}, text.Colourf("<green>Claim Info</green>"))
	fac := user.GetUser(pl).Data.Faction.Faction()

	fm = fm.WithBody(text.Colourf(
		"<green>Maximum Chunks Allowed: <yellow>%v</yellow>\n"+
			"<green>Remaining chunks to claim: <yellow>%v</yellow> chunks",
		fac.MaxChunksAllowed(),
		fac.RemainingChunks(),
	))
	pl.SendForm(fm)
}
