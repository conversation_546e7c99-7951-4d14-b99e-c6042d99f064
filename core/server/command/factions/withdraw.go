package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/factions/items"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

type WithdrawCommand struct {
	Amount float64 `cmd:"amount"`
}

func (w WithdrawCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.Data.Faction.Stats.Doubloons >= w.Amount {
			if _, err := pl.Inventory().AddItem(items.BankNote{Amount: w.Amount}.Stack()); err != nil {
				pl.Drop(items.BankNote{Amount: w.Amount}.Stack())
				pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
			}

			u.Data.Faction.Stats.Doubloons -= w.Amount
			o.Print(text.Colourf(language.Translate(pl).ObtainedBankNote, server.Config.Prefix, utils.ShortenNumber(w.Amount, 2)))
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotEnoughDoubloons))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
