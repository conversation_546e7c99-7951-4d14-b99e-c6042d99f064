package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/database"
	"server/server/factions/items"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

type BankDepositCommand struct {
	Deposit cmd.SubCommand `cmd:"deposit"`
	Amount  float64        `cmd:"amount"`
}

func (b BankDepositCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.Data.Faction.HasFaction() {
			if d := u.Data.Faction.Stats.Doubloons; d >= b.Amount {
				u.Data.Faction.Stats.Doubloons -= b.Amount
				u.Data.Faction.Faction().BankDoubloons += b.Amount
				o.Print(text.Colourf(language.Translate(pl).Commands.Bank.DepositSuccess, server.Config.Prefix, utils.ShortenNumber(b.Amount, 2)))
			} else {
				o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotEnoughDoubloons))
			}
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotInFaction))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type BankViewCommand struct {
	View cmd.SubCommand `cmd:"view"`
}

func (BankViewCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if u := user.GetUser(pl); u.Data.Faction.HasFaction() {
			o.Print(text.Colourf(language.Translate(pl).Commands.Bank.ViewSuccess, server.Config.Prefix, utils.ShortenNumber(u.Data.Faction.Faction().BankDoubloons, 5)))
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotInFaction))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type BankWithdrawCommand struct {
	Withdraw cmd.SubCommand `cmd:"withdraw"`
	Amount   float64        `cmd:"amount"`
}

func (b BankWithdrawCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if u := user.GetUser(pl); u.Data.Faction.HasFaction() {
			fac := u.Data.Faction.Faction()
			if fac.BankDoubloons >= b.Amount {
				fac.BankDoubloons -= b.Amount
				utils.Panic(database.DB.SaveFaction(fac))
				if _, err := pl.Inventory().AddItem(items.BankNote{Amount: b.Amount}.Stack()); err != nil {
					pl.Drop(items.BankNote{Amount: b.Amount}.Stack())
					pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
				}

				o.Print(text.Colourf(language.Translate(pl).ObtainedBankNote, server.Config.Prefix, utils.ShortenNumber(b.Amount, 2)))
			} else {
				o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotEnoughBankDoubloons))
			}
		} else {
			o.Error(text.Colourf(language.Translate(pl).Commands.Bank.NotInFaction))
		}
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
