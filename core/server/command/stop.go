package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	core "server/server"
	"server/server/database"
	"server/server/user"
	"server/server/utils"
)

type StopCommand struct{}

func (StopCommand) Allow(src cmd.Source) bool {
	return Stop.Test(src)
}

func (StopCommand) PermissionMessage(src cmd.Source) string {
	return Stop.PermissionMessage(src)
}

func (StopCommand) Run(_ cmd.Source, _ *cmd.Output, tx *world.Tx) {
	// Save world data first
	tx.World().Save()

	// Save all player data (both Dragonfly and custom data) before closing
	for pl := range core.MCServer.Players(nil) {
		// Save Dragonfly player data (inventory, position, health, etc.)
		if err := core.DragonflyConfig.PlayerProvider.Save(pl.UUID(), pl.Data(), core.MCServer.World()); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data: " + err.Error())
		}

		// Save custom player data (faction data, stats, etc.)
		if err := user.Save(pl); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
		}
	}

	// Save all cached data
	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("identifier", identifier).Error(err.Error())
	}

	// Now it's safe to close the server
	utils.Panic(core.MCServer.Close())
}
