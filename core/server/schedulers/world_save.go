package schedulers

import (
	"github.com/df-mc/dragonfly/server"
	"log/slog"
	core "server/server"
	"server/server/database"
	"server/server/user"
	"server/server/utils"
	"time"
)

func ScheduleWorldSaving(conf server.Config) {
	for range time.NewTicker(1 * time.Minute).C {
		core.MCServer.World().Save()

		for identifier, err := range database.DB.SaveAll() {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("identifier", identifier).Error(err.Error())
		}

		for pl := range core.MCServer.Players(nil) {
			// Save Dragonfly player data (inventory, position, health, etc.)
			if err := conf.PlayerProvider.Save(pl.UUID(), pl.Data(), core.MCServer.World()); err != nil {
				errorCode := utils.RandString(6)
				slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data during scheduled save: " + err.Error())
			}

			// Save custom player data (faction data, stats, etc.)
			if err := user.Save(pl); err != nil {
				errorCode := utils.RandString(6)
				slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data during scheduled save: " + err.Error())
			}
			//pl.Message(text.Colourf(language.Translate(pl).WorldSaved, core.Config.Prefix, time.Now().Sub(old).Milliseconds()))
		}
	}
}
