package database

import "github.com/sandertv/gophertunnel/minecraft/text"

type Rank int

const (
	Owner Rank = iota
	Admin
	Secretary
	Moderator
	Support
	Builder
	Trainee
	Youtuber
	MGP
	MLP
	MMP
	MVP
	VIP
	Player
)

var ShortenedRanks = []string{
	"owner", "admin", "secretary", "moderator", "support",
	"builder", "trainee", "youtuber", "mgp", "mlp",
	"mmp", "mvp", "vip", "player",
}

var RankPrefixes = []string{
	"<aqua>Owner</aqua> ", "<dark-aqua>Admin</dark-aqua> ", "<amethyst>Secretary</amethyst> ", "<green>Moderator</green> ", "<blue>Support</blue> ",
	"<black>Builder</black> ", "<white>Trainee</white> ", "<dark-red>You</dark-red><white>Tuber</white> ", "<red>MGP</red> ", "<dark-aqua>MLP</dark-aqua> ",
	"<gold>MMP</gold> ", "<yellow>MVP</yellow> ", "<green>VIP</green> ", "",
}

func (r Rank) Shortened() string {
	return ShortenedRanks[r]
}

func (r Rank) Prefix() string {
	return text.Colourf(RankPrefixes[r])
}

func RankFromPrefix(prefix string) Rank {
	id := Owner
	for _, rank := range RankPrefixes {
		if text.Colourf(rank) == prefix {
			return id
		}
		id++
	}

	return -1
}

func RankFromName(name string) Rank {
	id := Owner
	for _, rank := range ShortenedRanks {
		if rank == name {
			return id
		}
		id++
	}

	return -1
}

type Role int

const (
	Member Role = iota
	CoLeader
	Leader
)

var RolePrefixes = []string{
	"<grey>Member</grey> ",
	"<white>Co-Leader</white> ",
	"<white>Leader</white> ",
}
