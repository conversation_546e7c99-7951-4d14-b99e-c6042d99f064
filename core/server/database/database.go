package database

import (
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/mongo"
)

var DB Database

type Database interface {
	Type() string
	Client() *mongo.Client

	CreatePlayer(data *PlayerData) error
	SavePlayer(data *PlayerData) error
	DeletePlayer(uuid uuid.UUID) error
	FindPlayer(uuid uuid.UUID) (*PlayerData, error)
	FindPlayerByName(playerName string) (*PlayerData, error)

	CreateFaction(data *FactionData) error
	SaveFaction(data *FactionData) error
	DeleteFaction(factionName string) error
	FindFaction(factionName string) (*FactionData, error)
	CachedFactions() map[string]*FactionData
	ForEachCachedFaction(fn func(*FactionData) bool)
	LoadAllFactionsIntoCache() error

	SaveAll() map[string]error
}
