package items

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	core "server/server"
	"server/server/blocks"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
)

func init() {
	blocks.RegisterSpecialItem(blocks.BankNote, BankNote{})
}

type BankNote struct {
	item.Paper
	Amount float64
}

func (bn BankNote) Stack() item.Stack {
	s := item.NewStack(item.Paper{}, 1).WithValue("special_item", int16(blocks.BankNote)).WithValue("amount", bn.Amount).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
	s = s.WithCustomName(text.Colourf("<emerald>Bank Note <white>[<dark-grey>%v</dark-grey> <yellow>doubloons</yellow>]</white></emerald>", utils.ShortenNumber(bn.Amount, 0))).WithLore("Left click to earn doubloons")
	return s
}

func (BankNote) Use(tx *world.Tx, usr item.User, ctx *item.UseContext) bool {
	pl := usr.(*player.Player)
	main, _ := pl.HeldItems()
	if amount, ok := main.Value("amount"); ok {
		a := amount.(float64)
		u := user.GetUser(pl)
		u.Data.Faction.Stats.Doubloons += a
		pl.Message(text.Colourf(language.Translate(pl).ObtainedBankNote, core.Config.Prefix, utils.ShortenNumber(a, 2)))
	}

	ctx.CountSub = 1
	return true
}

func (bn BankNote) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return bn.Use(tx, usr, ctx)
}
