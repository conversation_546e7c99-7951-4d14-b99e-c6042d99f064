package factions

import (
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/scoreboard"
	"github.com/samber/lo"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/font"
	"server/server/user"
	"server/server/utils"
)

func SendMainScoreboard(pl *player.Player) {
	u := user.GetUser(pl)
	u.Scoreboard = scoreboard.New(text.Colourf("<dark-red>MMC <gold>Factions</gold></dark-red>"))
	u.Scoreboard.Set(1, "§0")
	u.Scoreboard.Set(2, text.Colourf("<grey>Player: <green>%v</green></grey>", pl.Name()))
	u.Scoreboard.Set(3, text.Colourf("<grey>Rank: <green>%v</green></grey>", u.Data.Rank().Prefix()))
	u.Scoreboard.Set(4, text.Colourf("<grey>Faction: <green>%v</green></grey>", lo.If(u.Data.Faction.HasFaction(), u.Data.Faction.Name).Else("None")))
	u.Scoreboard.Set(5, "§1")
	u.Scoreboard.Set(6, text.Colourf("<grey>Strength: <green>%v</green></grey>", u.Data.Faction.Stats.Strength))
	u.Scoreboard.Set(7, text.Colourf("<grey>Doubloons: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Stats.Doubloons, 0)))
	u.Scoreboard.Set(8, "§2")
	u.Scoreboard.Set(9, text.Colourf("<grey>Kills: <green>%v</green></grey>", u.Data.Faction.Stats.Kills))
	u.Scoreboard.Set(10, text.Colourf("<grey>Kill Streak: <green>%v</green></grey>", u.Data.Faction.Stats.KillStreak))
	u.Scoreboard.Set(11, text.Colourf("<grey>KDR: <green>%v</green></grey>", utils.ShortenNumber(u.Data.Faction.Stats.KDR(), 2)))
	u.Scoreboard.Set(12, "§3")
	u.Scoreboard.Set(13, font.Transform("MASSACREMC.NET"))
	u.SendScoreboard(7)
}
