package user

import (
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/database"
)

type nameConfig struct {
	ShowRank    bool
	ShowFaction bool
}

func (nc nameConfig) Name(pd *database.PlayerData) string {
	var facStr string
	if nc.ShowFaction && pd.Faction.HasFaction() {
		facStr = text.Colourf("<bold><white>[<aqua>%v</aqua>]</white></bold> ", pd.Faction.Name)
	}

	if nc.ShowRank {
		return text.Colourf("%v%v<grey>%v</grey>", facStr, pd.Rank().Prefix(), pd.Username)
	}

	return text.Colourf("<grey>%v</grey>", pd.Username)
}

var FactionNameDisplay = nameConfig{ShowRank: true, ShowFaction: true}
