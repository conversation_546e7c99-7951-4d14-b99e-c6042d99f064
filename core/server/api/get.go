package api

import (
	"github.com/google/uuid"
	"net/http"
	"server/server/database"
	"server/server/utils"

	"github.com/gin-gonic/gin"
)

func initGetRequests(rg *gin.RouterGroup) {
	rg.GET("/players/:uuid", jwtAuthMiddleware(), func(c *gin.Context) {
		id := utils.Panics(uuid.Parse(c.Param("uuid")))
		pd, err := database.DB.FindPlayer(id)
		if err != nil {
			panic(err)
		}
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"data": pd,
		})
	})

	rg.GET("/factions/:name", jwtAuthMiddleware(), func(c *gin.Context) {
		name := c.<PERSON>("name")
		fd, err := database.DB.FindFaction(name)
		if err != nil {
			panic(err)
		}
		c.JSON(http.StatusOK, gin.H{
			"data": fd,
		})
	})
}
